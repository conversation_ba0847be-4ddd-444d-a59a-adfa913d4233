"use client";

import {
  Navbar as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Navbar<PERSON>ontent,
  Navbar<PERSON><PERSON>,
  NavbarItem,
} from "@heroui/navbar";
import NextLink from "next/link";
import Image from "next/image";
import { useState } from "react";
import { usePathname } from "next/navigation";
import { siteConfig } from "@/config/site";
import { useLanguage } from "@/contexts/LanguageContext";
import AppDrawer from "./AppDrawer";

export const Navbar = () => {
  const { language, setLanguage, t, isRTL } = useLanguage();
  const pathname = usePathname();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  return (
    <>
      <HeroUINavbar
        maxWidth="xl"
        position="sticky"
        className={`bg-[#1d4930] backdrop-blur-md border-b-2 border-secondary shadow-sm ${isRTL ? "rtl" : "ltr"}`}
      >
        {/* Left logo */}
        <NavbarContent justify="start">
          <NavbarBrand>
            <NextLink href="/" className="flex items-center gap-2">
              <Image src="/pmw.png" alt="Logo" width={32} height={32} />
              <span className="text-white font-bold">PWM</span>
            </NextLink>
          </NavbarBrand>
        </NavbarContent>

        {/* Center Nav Items */}
        <NavbarContent justify="center" className="hidden md:flex">
          {siteConfig.navItems.map((item) => (
            <NavbarItem key={item.href}>
              <NextLink
                href={item.href}
                className={`text-white/90 hover:text-white font-medium ${
                  pathname === item.href ? "border-b-2 border-secondary" : ""
                }`}
              >
                {t(item.translationKey)}
              </NextLink>
            </NavbarItem>
          ))}
        </NavbarContent>

        {/* Right: Language + Drawer button */}
        <NavbarContent justify="end" className="items-center gap-4">
          <div className="flex gap-2 text-sm">
            <button
              className={
                language === "EN" ? "text-white font-bold" : "text-white/70"
              }
              onClick={() => setLanguage("EN")}
            >
              EN
            </button>
            <span className="text-white/50">|</span>
            <button
              className={
                language === "AR" ? "text-white font-bold" : "text-white/70"
              }
              onClick={() => setLanguage("AR")}
            >
              AR
            </button>
          </div>
          <button
            onClick={() => setIsDrawerOpen(true)}
            className="bg-secondary text-white px-4 py-2 rounded-lg hover:bg-secondary/80"
          >
            Open Drawer
          </button>
        </NavbarContent>
      </HeroUINavbar>

      {/* Drawer Component */}
      <AppDrawer isOpen={isDrawerOpen} onClose={() => setIsDrawerOpen(false)} />
    </>
  );
};
