// components/AppDrawer.jsx
"use client";

import {
  <PERSON><PERSON>,
  Drawer<PERSON>ontent,
  <PERSON>er<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>ooter,
  <PERSON><PERSON>,
} from "@heroui/react";

export default function AppDrawer({ isOpen, onClose }) {
  return (
    <Drawer isOpen={isOpen} onOpenChange={onClose}>
      <DrawerContent>
        {(close) => (
          <>
            <DrawerHeader className="flex flex-col gap-1">
              Drawer Title
            </DrawerHeader>

            <DrawerBody>
              <p>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam
                pulvinar risus non risus hendrerit venenatis.
              </p>
              <p>
                Magna exercitation reprehenderit magna aute tempor cupidatat
                consequat elit dolor adipisicing.
              </p>
            </DrawerBody>

            <DrawerFooter>
              <Button color="danger" variant="light" onPress={close}>
                Close
              </Button>
              <Button color="primary" onPress={close}>
                Action
              </Button>
            </DrawerFooter>
          </>
        )}
      </DrawerContent>
    </Drawer>
  );
}
